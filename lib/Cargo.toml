[package]
name = "dpn_peernode_lib"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
thiserror = "1.0"
futures = "0.3"
tokio = { version = "1.3", features = ["rt-multi-thread", "macros"] }
async-trait = "0.1"
bytes = "1.0"
pin-project-lite = "0.2"
url = "2.2"
serde = { version = "1.0", features = ["derive"] }
anyhow = "1.0.62"
rustls = "0.21.7"
rustls-pemfile = "1.0.1"
webpki-roots = "0.25.2"
rcgen = "0.11.1"
quinn = "0.10.2"
bincode = "1.3.3"
reqwest = { version = "0.11.13", features = ["json"] }
serde_json = "1.0.91"
async-speed-limit = { version = "0.4.1", features = ["tokio"] }
log = "0.4.20"
openssl = { version = "0.10.35", features = ["vendored"] }
tokio-tungstenite = "0.15"
