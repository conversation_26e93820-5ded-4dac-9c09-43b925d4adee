mod bufio;
mod copy;

use std::io::BufReader;

pub use bufio::BufIoExt;
pub use copy::DuplexCopy;
use rustls::Certificate;
use rustls_pemfile::certs;

/// parse content of a cert. If there is no cert, leave it empty string.
///
/// Example cert:
/// ```
/// -----BEGIN CERTIFICATE-----
/// MIIDGzCCAgOgAwIBAgIUcB4uNzmGOSEW1gJVMtj7mqAMazIwDQYJKoZIhvcNAQEL
/// ...
/// oV9Dpts9z1mh79DkoFts3auzs5Q8LgUi7YTmURomhw==
/// -----E<PERSON> CERTIFICATE-----
/// ```
///
pub fn parse_cert_root_ca(cert_content: String) -> Option<Certificate> {
    // parse root CA of master
    let cert_file = &mut BufReader::new(cert_content.as_bytes());
    let cert_chain = certs(cert_file).unwrap();
    let cert_chain = cert_chain
        .iter()
        .map(|x| Certificate(x.clone()))
        .collect::<Vec<Certificate>>();
    cert_chain.first().map(|c| c.clone())
}
