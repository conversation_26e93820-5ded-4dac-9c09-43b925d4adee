use crate::types::AssignMasternodeRes;
use crate::types::AuthTokens;
use crate::types::MasternodeInfo;
use crate::types::SSORes;
use anyhow::anyhow;
use anyhow::Error;
use anyhow::Result;
use reqwest::Client;
use reqwest::StatusCode;
use std::{fmt::Debug, sync::Arc};
use tokio::sync::RwLock;

#[derive(Debug)]
pub struct AdminServiceImpl {
    auth_tokens: Arc<RwLock<AuthTokens>>,

    pub base_url: String,
    // paths
    assign_masternode_path: String,
    refresh_token_path: String,
}

impl AdminServiceImpl {
    pub fn new(base_url: String, auth_tokens: Arc<RwLock<AuthTokens>>) -> Self {
        let base_url: String = match base_url.strip_suffix('/') {
            Some(url) => url.to_string(),
            None => base_url.clone(),
        };

        Self {
            auth_tokens,
            base_url: base_url.clone(),
            assign_masternode_path: format!(
                "{}/{}",
                base_url.clone(),
                "connections/assign_masternode"
            ),
            refresh_token_path: format!("{}/{}", base_url.clone(), "auth/refresh_token"),
        }
    }

    pub async fn assign_masternode(self: Arc<Self>) -> Result<MasternodeInfo, Error> {
        let _self = self.clone();
        let client = self.get_client().await;
        match client
            .get(_self.assign_masternode_path.clone())
            .send()
            .await
        {
            Ok(res) => match res.status() {
                StatusCode::OK => match res.json::<AssignMasternodeRes>().await {
                    Ok(assign_masternode_rs) => match assign_masternode_rs.masternode {
                        Some(masternode) => return Ok(masternode),
                        None => return Err(anyhow!("no masternode is online")),
                    },
                    Err(_) => return Err(anyhow!("decode masternode json failed")),
                },
                StatusCode::UNAUTHORIZED => {
                    _ = _self.refresh_token().await;
                    return Err(anyhow!("unauthorized to assign masternode"));
                }
                _ => return Err(anyhow!("unknown status code")),
            },
            Err(_) => return Err(anyhow!("cannot send request to admin service")),
        }
    }

    pub async fn refresh_token(self: Arc<Self>) -> Result<AuthTokens> {
        let auth_tokens = self.clone().get_auth_tokens().await;

        let client = reqwest::Client::new();
        match client
            .post(self.refresh_token_path.clone())
            .json(&auth_tokens)
            .send()
            .await
        {
            Ok(res) => match res.status() {
                StatusCode::OK => match res.json::<SSORes>().await {
                    Ok(sso_res) => match sso_res.code {
                        1 => {
                            let auth_tokens = AuthTokens {
                                access_token: sso_res.access_token.unwrap(),
                                refresh_token: sso_res.refresh_token.unwrap(),
                            };
                            let mut auth_tokens_wlk = self.auth_tokens.write().await;
                            *auth_tokens_wlk = auth_tokens.clone();
                            drop(auth_tokens_wlk);

                            Ok(auth_tokens.clone())
                        }
                        _ => return Err(anyhow!("failed to refresh token")),
                    },
                    Err(_) => return Err(anyhow!("decode json failed")),
                },
                _ => return Err(anyhow!("get assign masternode failed")),
            },
            Err(_) => return Err(anyhow!("get assign masternode failed")),
        }
    }

    pub async fn get_auth_tokens(self: Arc<Self>) -> AuthTokens {
        let auth_tokens_rlk = self.auth_tokens.read().await;
        let auth_tokens = auth_tokens_rlk.clone();
        auth_tokens
    }

    async fn get_client(self: Arc<Self>) -> Client {
        let access_token = self.get_auth_tokens().await.access_token;

        Client::builder()
            .default_headers({
                let mut headers = reqwest::header::HeaderMap::new();
                headers.insert(
                    reqwest::header::AUTHORIZATION,
                    reqwest::header::HeaderValue::from_str(&format!("Bearer {}", access_token))
                        .unwrap(),
                );
                headers
            })
            .build()
            .unwrap()
    }
}
