use serde::{Deserialize, Serialize};

use crate::types::AuthTokens;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum DaemonEvent {
    // events send by flutter
    StartConnection(AuthTokens),
    StopConnection(),

    // events send by daemon
    Info(String),
    MessageParsingError(String),
    ConnectionStopped(String),
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn sample_events_json() {
        let events = vec![
            // DaemonEvent::// events send by flutter
            DaemonEvent::StartConnection(AuthTokens {
                access_token: "access_token".to_string(),
                refresh_token: "refresh_token".to_string(),
            }),
            DaemonEvent::StopConnection(),
            // DaemonEvent::// events send by daemon
            DaemonEvent::MessageParsingError("error".to_string()),
            DaemonEvent::Info("info".to_string()),
            DaemonEvent::ConnectionStopped("stopped".to_string()),
        ];

        let events_json = events.iter().map(|e| serde_json::to_string(&e).unwrap());
        for ejson in events_json {
            println!("{:?}", ejson)
        }
    }
}
