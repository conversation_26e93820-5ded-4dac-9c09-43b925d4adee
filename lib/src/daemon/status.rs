use serde::{Deserialize, Serialize};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize, PartialEq)]
pub enum ConnectionStatus {
    NotConnected,
    Connecting,
    Connected,
    Disconnecting,
    Error(String),
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct StatusUpdate {
    pub status: ConnectionStatus,
    pub message: Option<String>,
    pub timestamp: u64,
}

impl StatusUpdate {
    pub fn new(status: ConnectionStatus, message: Option<String>) -> Self {
        Self {
            status,
            message,
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs(),
        }
    }
}
