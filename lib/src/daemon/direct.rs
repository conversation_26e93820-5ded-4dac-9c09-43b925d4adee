use std::{sync::Arc, time::Duration};
use anyhow::Result;
use log::{debug, error, info};
use tokio::{sync::{mpsc::{UnboundedReceiver, UnboundedSender}, Mute<PERSON>, RwLock}, task::Jo<PERSON><PERSON><PERSON><PERSON>};

use crate::{core::{start, ConnErrResult}, integration::AdminServiceImpl, types::AuthTokens};

use super::status::{ConnectionStatus, StatusUpdate};

#[derive(Debug, Clone)]
pub struct DirectPeerNodeDaemonConfig {
    pub admin_addr: String,
    pub auth_tokens: AuthTokens,
}

pub struct DirectPeerNodeDaemon {
    config: DirectPeerNodeDaemonConfig,
    auth_tokens: Arc<RwLock<AuthTokens>>,
    admin_svc: Arc<AdminServiceImpl>,
    
    // Connection state
    current_status: Arc<RwLock<ConnectionStatus>>,
    proxy_task: Arc<Mutex<Option<JoinHandle<()>>>>,
    
    // Status updates channel
    status_tx: UnboundedSender<StatusUpdate>,
    status_rx: Arc<Mutex<UnboundedReceiver<StatusUpdate>>>,
    
    // Shutdown control
    shutdown_tx: UnboundedSender<()>,
    shutdown_rx: Arc<Mutex<UnboundedReceiver<()>>>,
}

impl DirectPeerNodeDaemon {
    pub fn new(config: DirectPeerNodeDaemonConfig) -> Self {
        let auth_tokens = Arc::new(RwLock::new(config.auth_tokens.clone()));
        let admin_svc = Arc::new(AdminServiceImpl::new(
            config.admin_addr.clone(),
            auth_tokens.clone(),
        ));

        let (status_tx, status_rx) = tokio::sync::mpsc::unbounded_channel::<StatusUpdate>();
        let (shutdown_tx, shutdown_rx) = tokio::sync::mpsc::unbounded_channel::<()>();

        Self {
            config,
            auth_tokens,
            admin_svc,
            current_status: Arc::new(RwLock::new(ConnectionStatus::NotConnected)),
            proxy_task: Arc::new(Mutex::new(None)),
            status_tx,
            status_rx: Arc::new(Mutex::new(status_rx)),
            shutdown_tx,
            shutdown_rx: Arc::new(Mutex::new(shutdown_rx)),
        }
    }

    pub async fn start_connection(self: Arc<Self>) -> Result<()> {
        info!("Starting connection...");
        
        // Check if already running
        let task_guard = self.proxy_task.lock().await;
        if task_guard.is_some() {
            info!("Connection already running");
            return Ok(());
        }
        drop(task_guard);

        // Update status
        self.update_status(ConnectionStatus::Connecting, Some("Initializing connection".to_string())).await;

        // Spawn the proxy task
        let daemon = self.clone();
        let task = tokio::spawn(async move {
            daemon.run_proxy_loop().await;
        });

        let mut task_guard = self.proxy_task.lock().await;
        *task_guard = Some(task);

        Ok(())
    }


    pub async fn stop_connection(self: Arc<Self>) -> Result<()> {
        info!("Stopping connection...");
        
        self.update_status(ConnectionStatus::Disconnecting, Some("Shutting down connection".to_string())).await;

        // Send shutdown signal
        let _ = self.shutdown_tx.send(());

        // Wait for task to complete
        let mut task_guard = self.proxy_task.lock().await;
        if let Some(task) = task_guard.take() {
            let _ = task.await;
        }

        self.update_status(ConnectionStatus::NotConnected, Some("Connection stopped".to_string())).await;
        
        Ok(())
    }

    pub async fn get_current_status(&self) -> ConnectionStatus {
        let status = self.current_status.read().await;
        status.clone()
    }

    pub async fn poll_status_update(&self) -> Option<StatusUpdate> {
        let mut rx = self.status_rx.lock().await;
        rx.recv().await
    }


    async fn update_status(&self, status: ConnectionStatus, message: Option<String>) {
        // Update internal status
        let mut current = self.current_status.write().await;
        *current = status.clone();
        drop(current);

        // Send status update
        let update = StatusUpdate::new(status, message);
        let _ = self.status_tx.send(update.clone());
        info!("Status update sent: {:?}", update.clone());
    }

    pub async fn run_proxy_loop(self: Arc<Self>) {
        debug!("Running proxy loop...");
        let mut shutdown_rx = self.shutdown_rx.lock().await;
        
        loop {
            tokio::select! {
                _ = shutdown_rx.recv() => {
                    info!("Received shutdown signal");
                    break;
                }
                _ = self.run_proxy_connection() => {
                    info!("Proxy connection ended, retrying in 5 seconds...");
                    self.update_status(
                        ConnectionStatus::Error("Connection lost".to_string()), 
                        Some("Reconnecting in 5 seconds...".to_string())
                    ).await;
                    
                    // Check for shutdown during retry delay
                    tokio::select! {
                        _ = shutdown_rx.recv() => {
                            info!("Received shutdown signal during retry");
                            break;
                        }
                        _ = tokio::time::sleep(Duration::from_secs(5)) => {
                            continue;
                        }
                    }
                }
            }
        }
    }

    async fn run_proxy_connection(&self) -> Result<()> {
        let admin_svc = self.admin_svc.clone();

        self.update_status(
            ConnectionStatus::Connecting, 
            Some("Assigning masternode...".to_string())
        ).await;

        match admin_svc.clone().assign_masternode().await {
            Ok(masternode) => {
                info!("Masternode assigned: {:?}", masternode);
                self.update_status(
                    ConnectionStatus::Connecting,
                    Some("Connecting to masternode...".to_string())
                ).await;

                let auth_tokens = admin_svc.clone().get_auth_tokens().await;
                
                match start(auth_tokens.clone(), masternode).await {
                    Ok(()) => {
                        self.update_status(
                            ConnectionStatus::Connected,
                            Some("Successfully connected".to_string())
                        ).await;
                        Ok(())
                    }
                    Err(e) => {
                        error!("Proxy server error: {:?}", e);
                        
                        // Handle specific errors
                        match e {
                            ConnErrResult::InvalidAuth => {
                                self.update_status(
                                    ConnectionStatus::Error("Authentication failed".to_string()),
                                    Some("Refreshing authentication tokens...".to_string())
                                ).await;
                                
                                match admin_svc.refresh_token().await {
                                    Ok(_) => {
                                        info!("Auth tokens refreshed successfully");
                                    }
                                    Err(e) => {
                                        error!("Failed to refresh auth tokens: {}", e);
                                    }
                                }
                            }
                            _ => {
                                self.update_status(
                                    ConnectionStatus::Error(format!("{:?}", e)),
                                    None
                                ).await;
                            }
                        }
                        
                        Err(anyhow::anyhow!("Proxy connection failed: {:?}", e))
                    }
                }
            }
            Err(e) => {
                error!("Failed to assign masternode: {:?}", e);
                self.update_status(
                    ConnectionStatus::Error("Failed to assign masternode".to_string()),
                    Some(e.to_string())
                ).await;
                Err(e)
            }
        }
    }

    pub async fn update_auth_tokens(&self, auth_tokens: AuthTokens) -> Result<()> {
        let mut at_wlock = self.auth_tokens.write().await;
        *at_wlock = auth_tokens;
        Ok(())
    }

}