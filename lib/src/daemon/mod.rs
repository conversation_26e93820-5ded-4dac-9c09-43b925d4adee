use crate::types::AuthTokens;
use anyhow::Result;
use futures::stream::SplitSink;
use futures::{SinkExt, StreamExt};
use log::{error, info};
use std::net::SocketAddr;
use std::sync::Arc;
use std::time::Duration;
use tokio::net::{TcpListener, TcpStream};
use tokio::sync::mpsc::{UnboundedReceiver, UnboundedSender};
use tokio::sync::Mutex;
use tokio::sync::RwLock;
use tokio_tungstenite::tungstenite::protocol::Message;
use tokio_tungstenite::tungstenite::Error;
use tokio_tungstenite::{accept_async, WebSocketStream};

pub mod event;
pub mod status;
pub mod direct;

use self::event::DaemonEvent;
use crate::{
    core::{start, ConnErrResult},
    integration::AdminServiceImpl,
};

/// Original WebSocket-based daemon implementation (kept for backward compatibility)
#[derive(Debug, Clone)]
pub struct PeerNodeDaemonConfig {
    pub webserver_addr: String,
    pub webserver_port: u16,
    pub admin_addr: String,
    pub auth_tokens: AuthTokens,
}

pub struct PeerNodeDaemon {
    config: PeerNodeDaemonConfig,
    auth_tokens: Arc<RwLock<AuthTokens>>,
    admin_svc: Arc<AdminServiceImpl>,
    proxy_running: RwLock<bool>,
    shutdown_proxy_tx: UnboundedSender<()>,
    shutdown_proxy_rx: Arc<Mutex<UnboundedReceiver<()>>>,
}

impl PeerNodeDaemon {
    pub fn new(config: PeerNodeDaemonConfig) -> Self {
        let auth_tokens = Arc::new(RwLock::new(config.auth_tokens.clone()));

        let admin_svc = Arc::new(AdminServiceImpl::new(
            config.admin_addr.clone(),
            auth_tokens.clone(),
        ));

        let (shutdown_proxy_tx, shutdown_proxy_rx) = tokio::sync::mpsc::unbounded_channel::<()>();

        Self {
            config: config.clone(),
            auth_tokens: auth_tokens.clone(),
            admin_svc,
            proxy_running: RwLock::new(false),
            shutdown_proxy_tx,
            shutdown_proxy_rx: Arc::new(Mutex::new(shutdown_proxy_rx)),
        }
    }

    pub async fn run(self: Arc<Self>) -> Result<()> {
        info!("running daemon...");
        let _self = self.clone();
        let _self2 = self.clone();
        loop {
            let admin_svc = _self2.clone().admin_svc.clone();

            info!("ASSIGNING_MASTERNODE");
            match admin_svc.clone().assign_masternode().await {
                Ok(masternode) => {
                    info!("RUNNING_PROXY_SERVER");
                    let auth_tokens = admin_svc.clone().get_auth_tokens().await;
                    if let Err(e) = start(auth_tokens.clone(), masternode).await {
                        error!("PROXY_SERVER_ERR={:#?}", e);
                        match e {
                            ConnErrResult::InvalidAuth => {
                                match _self2.clone().admin_svc.clone().refresh_token().await {
                                    Ok(_) => {
                                        info!("REFRESHED_AUTH_TOKENS");
                                    }
                                    Err(e) => {
                                        error!("REFRESHED_AUTH_TOKENS_FAILED={}", e)
                                    }
                                }
                            }
                            _ => {}
                        };
                        info!("RECONNECTING");
                        tokio::time::sleep(Duration::from_secs(5)).await;
                        continue;
                    };
                }
                Err(e) => {
                    error!("ASSIGNING_MASTERNODE_ERR={:#?}", e);
                    tokio::time::sleep(Duration::from_secs(5)).await;
                    continue;
                }
            }
        }
    }

    pub async fn run_with_websocket(self: Arc<Self>) -> Result<()> {
        info!("running daemon with websocket...");

        let config = self.config.clone();
        let addr =
            format!("{}:{}", config.webserver_addr, config.webserver_port).parse::<SocketAddr>()?;
        let listener = TcpListener::bind(&addr).await?;
        info!("ws server is listening on: {}", addr);

        while let Ok((stream, addr)) = listener.accept().await {
            let _self = self.clone();
            tokio::spawn(async move {
                _ = _self.handle_ws_connection(stream).await;
            });
            info!("ws connection established client_addr={}", addr.to_string())
        }

        Ok(())
    }

    async fn handle_ws_connection(
        self: Arc<Self>,
        stream: tokio::net::TcpStream,
    ) -> Result<(), Error> {
        // Accept the WebSocket connection
        let ws_stream = accept_async(stream)
            .await
            .expect("Error during WebSocket handshake");

        // Split the WebSocket stream
        let (tx, mut rx) = ws_stream.split();
        let safe_tx = Arc::new(RwLock::new(tx));

        // Handle messages from the client
        while let Some(Ok(message)) = rx.next().await {
            let tx = safe_tx.clone();
            if let Message::Text(message) = message {
                let _self = self.clone();
                if let Ok(event) = serde_json::from_str::<DaemonEvent>(&message) {
                    tokio::spawn(async move {
                        _self.clone().handle_ws_event(tx.clone(), event).await;
                    });
                } else {
                    error!("failed to decode daemon event");
                    _self
                        .clone()
                        .emit_ws_event(
                            tx.clone(),
                            DaemonEvent::MessageParsingError(format!(
                                "failed to decode daemon event message={}",
                                message
                            )),
                        )
                        .await;
                }
            }
        }

        Ok(())
    }

    async fn handle_ws_event(
        self: Arc<Self>,
        ws_tx: Arc<RwLock<SplitSink<WebSocketStream<TcpStream>, Message>>>,
        event: DaemonEvent,
    ) {
        match event {
            DaemonEvent::StartConnection(auth_tokens) => {
                let mut at_wlock = self.auth_tokens.write().await;
                at_wlock.access_token = auth_tokens.access_token;
                at_wlock.refresh_token = auth_tokens.refresh_token;
                drop(at_wlock);
                tokio::spawn(async move { self.clone().start_proxy_server(ws_tx).await });
            }
            DaemonEvent::StopConnection() => {
                tokio::spawn(async move { self.clone().stop_proxy_server(ws_tx).await });
            }
            e => {
                error!("unsupported event: {:#?}", e)
            }
        };
    }

    async fn emit_ws_event(
        self: Arc<Self>,
        ws_tx: Arc<RwLock<SplitSink<WebSocketStream<TcpStream>, Message>>>,
        event: DaemonEvent,
    ) {
        info!("Event: {:#?}", event);
        let mut tx_guard = ws_tx.write().await;
        if let Ok(json_event) = serde_json::to_string(&event) {
            _ = tx_guard.send(Message::Text(json_event)).await;
        }
    }

    async fn start_proxy_server(
        self: Arc<Self>,
        ws_tx: Arc<RwLock<SplitSink<WebSocketStream<TcpStream>, Message>>>,
    ) {
        let _self = self.clone();
        let _self2 = self.clone();
        let ws_tx2 = ws_tx.clone();
        let proxy_running = _self.proxy_running.read().await;
        let running = proxy_running.clone();
        drop(proxy_running);
        if running == false {
            let _self3 = _self2.clone();
            let _self4 = _self2.clone();
            let mut shutdown_proxy_rx = _self4.shutdown_proxy_rx.lock().await;

            tokio::select! {
                _ = shutdown_proxy_rx.recv() => {
                    info!("shutting down proxy server");
                    _self4
                    .clone()
                    .emit_ws_event(
                        ws_tx.clone(),
                        DaemonEvent::ConnectionStopped("PROXY_SERVER_SHUTTED_DOWN".to_string()),
                    )
                    .await;
                },
                _ = async move {
                    let mut proxy_running = _self3.proxy_running.write().await;
                    *proxy_running = true;
                    drop(proxy_running);

                    let ws_tx = ws_tx2.clone();
                    let admin_svc = _self3.clone().admin_svc.clone();

                    _self3
                        .clone()
                        .emit_ws_event(
                            ws_tx.clone(),
                            DaemonEvent::Info("ASSIGNING_MASTERNODE".to_string()),
                        )
                        .await;
                    match admin_svc.clone().assign_masternode().await {
                        Ok(masternode) => {
                            _self3
                                .clone()
                                .emit_ws_event(
                                    ws_tx.clone(),
                                    DaemonEvent::Info("RUNNING_PROXY_SERVER".to_string()),
                                )
                                .await;
                            let auth_tokens = admin_svc.clone().get_auth_tokens().await;
                            if let Err(e) = start(auth_tokens.clone(), masternode).await {
                                _self3
                                    .clone()
                                    .emit_ws_event(
                                        ws_tx.clone(),
                                        DaemonEvent::ConnectionStopped(format!(
                                            "PROXY_SERVER_ERR={:#?}",
                                            e
                                        )),
                                    )
                                    .await;
                                match e {
                                    ConnErrResult::InvalidAuth => {
                                        match _self2.clone().admin_svc.clone().refresh_token().await {
                                            Ok(_) => {
                                                _self3
                                                    .clone()
                                                    .emit_ws_event(
                                                        ws_tx.clone(),
                                                        DaemonEvent::Info(
                                                            "REFRESHED_AUTH_TOKENS".to_string(),
                                                        ),
                                                    )
                                                    .await;
                                            }
                                            Err(e) => {
                                                _self3
                                                    .clone()
                                                    .emit_ws_event(
                                                        ws_tx.clone(),
                                                        DaemonEvent::ConnectionStopped(format!(
                                                            "REFRESHED_AUTH_TOKENS_FAILED={}",
                                                            e
                                                        )),
                                                    )
                                                    .await;
                                            }
                                        }
                                    }
                                    _ => {}
                                };
                            }
                        }
                        Err(e) => {
                            _self3
                                .clone()
                                .emit_ws_event(
                                    ws_tx.clone(),
                                    DaemonEvent::ConnectionStopped(format!(
                                        "ASSIGNING_MASTERNODE_ERR={:#?}",
                                        e
                                    )),
                                )
                                .await;
                        }
                    }
                } => {},
            }
            let mut proxy_running = _self.proxy_running.write().await;
            *proxy_running = false;
        } else {
            info!("proxy server already running");
        };
    }

    async fn stop_proxy_server(
        self: Arc<Self>,
        ws_tx: Arc<RwLock<SplitSink<WebSocketStream<TcpStream>, Message>>>,
    ) {
        let _self = self.clone();
        let proxy_running = _self.proxy_running.read().await;
        let running = proxy_running.clone();
        if running {
            match self.shutdown_proxy_tx.clone().send(()) {
                Ok(_) => info!("sent shutdown signal"),
                Err(e) => error!("error sending shutdown signal: {}", e),
            };
        } else {
            info!("no connection is running");
            self.clone()
                .emit_ws_event(
                    ws_tx,
                    DaemonEvent::ConnectionStopped("NO_CONNECTION_IS_RUNNING".to_string()),
                )
                .await;
        }
    }
}
