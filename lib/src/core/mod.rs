pub mod address;
pub mod connector;
pub mod proxy_server;
pub mod quic;

use std::{sync::Arc, time::Duration};

use crate::{
    core::{
        connector::DirectConnector,
        proxy_server::ProxyServer,
        quic::{quic_connect, OpenBidirectionalStream},
    },
    util::parse_cert_root_ca,
};

pub use crate::error::ProxyError;
use crate::types::{AuthTokens, MasternodeInfo};
use anyhow::Result;
use log::{error, info};
use rustls::Certificate;
use serde::{Deserialize, Serialize};
use tokio::{io::{AsyncReadExt, AsyncWriteExt}, time::sleep};

#[derive(Serialize, Deserialize, PartialEq, Debug)]
pub enum ConnErrResult {
    InvalidAuth,
    SameIP,
    SpeedTestFailed,
    QuicFailed,
    SerializeAuthsFailed,
    OpenBidirectionalStreamFailed,
    StreamReadWriteFailed,
    ProxyServerErr(String),
    UnknownErr,
}

pub async fn start(
    auth_tokens: AuthTokens,
    masternode: MasternodeInfo,
) -> Result<(), ConnErrResult> {
    // info!("Connecting to masternode {:#?}", masternode);

    let root_ca: Option<Certificate> = masternode
        .root_ca
        .clone()
        .and_then(|rca| parse_cert_root_ca(rca));
    // info!(
    //     "Masternode rootCA: {}",
    //     masternode.root_ca.unwrap_or("".to_owned())
    // );

    let master_conn = {
        match quic_connect(&masternode.peer_bind, root_ca).await {
            Ok(conn) => Ok(conn),
            Err(e) => {
                error!("quic failed err={}", e);
                Err(e)
            }
        }
    }
    .map_err(|_| ConnErrResult::QuicFailed)?;
    let mut master_stream = master_conn
        .open_bidirectional_stream()
        .await
        .map_err(|_| ConnErrResult::OpenBidirectionalStreamFailed)?;
    let mut buff = [0; 1500];

    // speed test
    for _ in 0..5000 {
        master_stream
            .write_all(&buff)
            .await
            .map_err(|_| ConnErrResult::StreamReadWriteFailed)?;
    }

    // auth
    let login_bytes =
        bincode::serialize(&auth_tokens).map_err(|_| ConnErrResult::SerializeAuthsFailed)?;
    master_stream
        .write_all(&login_bytes)
        .await
        .map_err(|_| ConnErrResult::StreamReadWriteFailed)?;

    // connection result
    let mut buf = [0u8; 1];
    master_stream
        .read_exact(&mut buf)
        .await
        .map_err(|_| ConnErrResult::StreamReadWriteFailed)?;
    info!("{}", buf[0]);
    match buf[0] {
        0 => {} // success
        1 => {
            // same ip connected already
            info!("same ip");
            return Err(ConnErrResult::SameIP);
        }
        2 => {
            // invalid access token
            info!("invalid auth");
            return Err(ConnErrResult::InvalidAuth);
        }
        3 => {
            // speed test failed
            info!("speed test failed");
            return Err(ConnErrResult::SpeedTestFailed);
        }
        _ => {
            // unknown error
            info!("unknown error");
            return Err(ConnErrResult::UnknownErr);
        }
    }
    info!("Connected to masternode!");

    // tokio::spawn(async move {
    //     loop {
    //         for _ in 0..5000 {
    //             if master_stream.read_exact(&mut buff).await.is_err() {
    //                 ()
    //             }
    //         }
    //         for _ in 0..5000 {
    //             _ = master_stream
    //                 .write_all(&buff)
    //                 .await;
    //         }
    //     }
    // });

    let server = Arc::new(ProxyServer::new(DirectConnector, master_conn));
    if let Err(e) = server.clone().run().await {
        error!("server error: {}", e);
        return Err(ConnErrResult::ProxyServerErr(e.to_string()));
    }

    Ok(())
}
