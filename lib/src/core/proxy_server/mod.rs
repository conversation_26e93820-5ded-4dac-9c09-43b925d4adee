mod http;
mod socks5;

pub use http::HttpHandle;
pub use socks5::Socks5Handle;
use tokio::sync::mpsc::{UnboundedReceiver, UnboundedSender};
use tokio::sync::Mutex;

use super::connector::Connector;
use super::{address, connector};
use crate::core::quic::AcceptBidirectionalStream;
use crate::error::ProxyError;
use crate::util::BufIoExt;
use log::{debug, error, info, warn};

use async_speed_limit::Limiter;

use quinn::Connection;
use std::sync::{atomic::AtomicU64, Arc};
use tokio::io::{AsyncRead, AsyncWrite, BufReader};

pub static BANDWIDTH: AtomicU64 = AtomicU64::new(1024 * 1024 * 1024);

pub struct ProxyServer<C> {
    incoming: Connection,
    client_handle: Arc<ClientHandle<C>>,
    shutdown_tx: UnboundedSender<()>,
    shutdown_rx: Mutex<UnboundedReceiver<()>>,
}

impl<C> ProxyServer<C>
where
    C: Connector + Send + Sync + 'static,
    <C as Connector>::Transport: Unpin + Send,
{
    pub fn new(connector: C, incoming: Connection) -> Self {
        let (shutdown_tx, shutdown_rx) = tokio::sync::mpsc::unbounded_channel::<()>();

        Self {
            incoming,
            client_handle: Arc::new(ClientHandle::new(connector)),
            shutdown_tx,
            shutdown_rx: Mutex::new(shutdown_rx),
        }
    }
    pub async fn run(self: Arc<Self>) -> Result<(), ProxyError> {
        info!("Proxy server is running");

        loop {
            let mut shutdown_rx = self.shutdown_rx.lock().await;

            tokio::select! {
                _ = shutdown_rx.recv() => {
                    info!("proxy server received shutdown");
                }
                e = self.incoming.closed() => {
                    bail!("connection closed due to error: {}", e);
                }
                result = self.incoming.accept_bidirectional_stream() => {
                    match result {
                        Ok(stream) => {
                            let client_handle = self.client_handle.clone();
                            tokio::spawn(async move {
                                if let Err(e) = client_handle.handle(stream).await {
                                    warn!("handle fail: {}", e);
                                }
                            });
                        }
                        Err(e) => {
                            bail!("accept incoming fail: {}", e);
                        }
                    }
                }
            }
        }
    }

    pub async fn stop(self: Arc<Self>) {
        _ = self.shutdown_tx.send(());
    }
}

struct ClientHandle<C> {
    connector: C,

    socks5_handle: Socks5Handle,
    http_handle: HttpHandle,
}

impl<C> ClientHandle<C> {
    fn new(connector: C) -> Self {
        Self {
            connector,
            socks5_handle: Socks5Handle::new(),
            http_handle: HttpHandle::new(),
        }
    }
}

impl<C> ClientHandle<C>
where
    C: Connector,
    <C as Connector>::Transport: Unpin,
{
    async fn handle<T>(&self, sock: T) -> Result<(), ProxyError>
    where
        T: AsyncRead + AsyncWrite + Unpin,
    {
        let bandwidth = BANDWIDTH.load(std::sync::atomic::Ordering::SeqCst);
        info!("New connection established with bandwidth limit: {} bytes/s", bandwidth);
        let limiter = <Limiter>::new(bandwidth as f64);
        let sock = limiter.limit(sock);
        let mut stream = BufReader::new(sock);
        match stream.try_peek_byte().await {
            Ok(Some(0x05)) => {
                debug!("SOCKS5 protocol detected, forwarding to SOCKS5 handler");
                let result = self.socks5_handle.handle(&self.connector, stream).await;
                match &result {
                    Ok(_) => info!("SOCKS5 connection handled successfully"),
                    Err(e) => error!("SOCKS5 connection handling failed: {}", e),
                }
                result
            },
            Ok(Some(_)) => {
                debug!("HTTP protocol detected, forwarding to HTTP handler");
                let result = self.http_handle.handle(&self.connector, stream).await;
                match &result {
                    Ok(_) => info!("HTTP connection handled successfully"),
                    Err(e) => error!("HTTP connection handling failed: {}", e),
                }
                result
            },
            Ok(None) => {
                warn!("Connection terminated: received EOF with no data");
                Ok(())
            }
            Err(e) => {
                error!("Failed to establish connection: {}", e);
                bail!("read local socket fail: {}", e);
            }
        }
    }
}
