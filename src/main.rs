use anyhow::Result;
use dpn_peernode_lib::daemon::direct::{DirectPeerNodeDaemon, DirectPeerNodeDaemonConfig};
use dpn_peernode_lib::daemon::status::{ConnectionStatus, StatusUpdate};
use dpn_peernode_lib::types::AuthTokens;
use serde::{Deserialize, Serialize};
use std::sync::{Arc, Mutex};
use tokio::runtime::Runtime;

// Bridge API implementation for testing
//
// This module provides both async and sync versions of the bridge functions:
// - Async versions (e.g., start_connection_async) should be used within async contexts
// - Sync versions (e.g., start_connection) use a separate runtime for Flutter bridge compatibility
lazy_static::lazy_static! {
    static ref RUNTIME: Mutex<Runtime> = Mutex::new(Runtime::new().unwrap());
}

#[derive(Debug, Clone)]
pub struct PeerNodeController {
    daemon_handle: usize,
}

lazy_static::lazy_static! {
    static ref DAEMON_STORAGE: Mutex<Vec<Option<Arc<DirectPeerNodeDaemon>>>> = Mutex::new(Vec::new());
}

impl PeerNodeController {
    pub fn new(admin_addr: String, access_token: String, refresh_token: String) -> Self {
        let daemon = Arc::new(DirectPeerNodeDaemon::new(DirectPeerNodeDaemonConfig {
            admin_addr,
            auth_tokens: AuthTokens {
                access_token,
                refresh_token,
            },
        }));

        let mut storage = DAEMON_STORAGE.lock().unwrap();
        let handle = storage.len();
        storage.push(Some(daemon));

        Self {
            daemon_handle: handle,
        }
    }

    fn get_daemon(&self) -> Arc<DirectPeerNodeDaemon> {
        let storage = DAEMON_STORAGE.lock().unwrap();
        storage[self.daemon_handle].as_ref().unwrap().clone()
    }
}

pub fn initialize_peer_node(
    admin_addr: String,
    access_token: String,
    refresh_token: String,
) -> PeerNodeController {
    PeerNodeController::new(admin_addr, access_token, refresh_token)
}

// Async versions for use within async contexts (like tests)
pub async fn start_connection_async(controller: &PeerNodeController) -> Result<(), String> {
    let daemon = controller.get_daemon();
    daemon.start_connection().await.map_err(|e| e.to_string())
}

pub async fn stop_connection_async(controller: &PeerNodeController) -> Result<(), String> {
    let daemon = controller.get_daemon();
    daemon.stop_connection().await.map_err(|e| e.to_string())
}

pub async fn get_connection_status_async(controller: &PeerNodeController) -> ConnectionStatus {
    let daemon = controller.get_daemon();
    daemon.get_current_status().await
}

pub async fn poll_status_update_async(controller: &PeerNodeController) -> Option<StatusUpdate> {
    let daemon = controller.get_daemon();
    daemon.poll_status_update().await
}

// Sync versions for use from non-async contexts (Flutter bridge)
pub fn start_connection(controller: &PeerNodeController) -> Result<(), String> {
    let daemon = controller.get_daemon();

    let rt = RUNTIME.lock().unwrap();
    rt.block_on(async { daemon.start_connection().await.map_err(|e| e.to_string()) })
}

pub fn stop_connection(controller: &PeerNodeController) -> Result<(), String> {
    let daemon = controller.get_daemon();

    let rt = RUNTIME.lock().unwrap();
    rt.block_on(async { daemon.stop_connection().await.map_err(|e| e.to_string()) })
}

pub fn get_connection_status(controller: &PeerNodeController) -> ConnectionStatus {
    let daemon = controller.get_daemon();

    let rt = RUNTIME.lock().unwrap();
    rt.block_on(async { daemon.get_current_status().await })
}

pub fn poll_status_update(controller: &PeerNodeController) -> Option<StatusUpdate> {
    let daemon = controller.get_daemon();

    let rt = RUNTIME.lock().unwrap();
    rt.block_on(async { daemon.poll_status_update().await })
}

#[tokio::main(flavor = "multi_thread", worker_threads = 10)]
async fn main() -> Result<()> {
    env_logger::init_from_env(env_logger::Env::new().default_filter_or("info"));

    println!("=== PeerNode Bridge API Test ===\n");

    let config = parse_config()?;

    // Test 1: Initialize PeerNode
    println!("1. Initializing PeerNode...");
    let controller_id = initialize_peer_node(
        config.admin_addr.clone(),
        config.access_token.clone(),
        config.refresh_token.clone(),
    );
    println!("   ✓ Controller initialized with ID: {:?}", controller_id.clone());

    // Test 2: Get initial status
    println!("\n2. Getting initial status...");
    let status = get_connection_status_async(&controller_id).await;
    println!("   ✓ Initial status: {:?}", status);

    // Test 3: Start connection
    println!("\n3. Starting connection...");
    match start_connection_async(&controller_id).await {
        Ok(()) => println!("   ✓ Connection started successfully"),
        Err(e) => println!("   ✗ Failed to start connection: {}", e),
    }

    // Test 4: Check status after starting
    println!("\n4. Checking status after start...");
    let status = get_connection_status_async(&controller_id).await;
    println!("   ✓ Status after start: {:?}", status);

    // Test 5: Wait a bit and check status again
    println!("\n5. Waiting 5 seconds and checking status...");
    tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
    let status = get_connection_status_async(&controller_id).await;
    println!("   ✓ Status after wait: {:?}", status);

    // Test 6: Stop connection
    println!("\n6. Stopping connection...");
    match stop_connection_async(&controller_id).await {
        Ok(()) => println!("   ✓ Connection stopped successfully"),
        Err(e) => println!("   ✗ Failed to stop connection: {}", e),
    }

    // Test 7: Final status check
    println!("\n7. Final status check...");
    let status = get_connection_status_async(&controller_id).await;
    println!("   ✓ Final status: {:?}", status);

    println!("\n=== Test Complete ===");
    Ok(())
}

#[derive(Deserialize, Serialize, Debug, Clone)]
struct Config {
    websocket_addr: String,
    websocket_port: u16,
    admin_addr: String,
    access_token: String,
    refresh_token: String,
}

fn parse_config() -> Result<Config> {
    if std::path::Path::new("./config.yaml").exists() {
        let mut file = std::fs::File::open("./config.yaml")?;
        let mut contents = String::new();
        std::io::Read::read_to_string(&mut file, &mut contents)?;
        let config = serde_yaml::from_str::<Config>(&contents)?;
        Ok(config)
    } else {
        panic!("failed to parse config file")
    }
}
