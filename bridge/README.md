
# Flutter Rust bridge codegen
### Rust nightly
Use nightly build for Rust:
```
rustup default nightly

# Or Apple Silicon chip
rustup default nightly-aarch64-apple-darwin
```

### Install dependencies
Following dependencies must be installed:
```
rustup target add aarch64-linux-android armv7-linux-androideabi x86_64-linux-android i686-linux-android aarch64-apple-ios aarch64-apple-ios-sim x86_64-apple-ios

cargo install cargo-ndk
cargo install cargo-expand
```

### Installing LLVM
package:ffigen uses LLVM. Install LLVM (9+) in the following way.

- Linux: Install libclangdev.
  - With apt-get: `sudo apt-get install libclang-dev`.
  - With dnf: `sudo dnf install clang-devel`.
- Windows: Install LLVM or `winget install -e --id LLVM.LLVM`.
- MacOS
    - Install Xcode command line tools - `xcode-select --install`.
    - Install LLVM - `brew install llvm`.

## Build Rust code for Flutter

### Clone `subnet-dpn-peernode` and `subnet-dpn-peer-app`
Open up Terminal at the working directory:
```
git clone https://github.com/unicornultralabs/subnet-dpn-peernode
git clone https://github.com/unicornultralabs/subnet-dpn-peer-app
```

Your working directory should look like this:
```
YOUR_WORKING_DIR/
    subnet-dpn-peernode/
    subnet-dpn-peer-app/
```

These repositories must be placed at the same directory since built files will be copied to `subnet-dpn-peer-app`.

### Build `.dart` interfaces
In your `subnet-dpn-peernode/bridge` folder, once you have edited `api.rs` to incorporate your own Rust code, the bridge files `bridge_definitions.dart` and `bridge_generated.dart` should be generated using the following command (note: append ` --wasm` to add web support):
```
cd subnet-dpn-peernode

flutter_rust_bridge_codegen --rust-input ./bridge/src/api.rs --dart-output ../subnet-dpn-peer-app/lib/rust/bridge_generated.dart --dart-decl-output ../subnet-dpn-peer-app/lib/rust/bridge_definitions.dart -c ../subnet-dpn-peer-app/ios/Runner/bridge_generated.h
```
Built Dart interfaces are at `subnet-dpn-peer-app/lib/rust`. Commit changes if any.

### Build `jniLibs` Android native libs
Make sure you have installed the dependencies (section. Install dependencies) for Android builds.

Then build out `.so` lib files for Flutter:
```terminal
cd subnet-dpn-peernode/bridge

# Build on ARM machine chip
cargo ndk  -t x86_64 -t x86 -t armeabi-v7a -t arm64-v8a -o ../../subnet-dpn-peer-app/android/app/src/main/jniLibs build --release
```
Built libs are at `subnet-dpn-peer-app/android/app/src/main/jniLibs`. Commit changes if any.

### Build iOS native libs
Install `lipo`:
```
cargo install cargo-lipo
```

Make sure you have installed the dependencies (section. Install dependencies) for iOS builds.
And use rustup default nightly-aarch64-apple-darwin when building universal for MacOS to minimize the build.

Then build the libs:
```
cd subnet-dpn-peernode/bridge

cargo lipo --release
```

Then copy the built lib:
```
cp ../target/universal/release/libdpn_bridge.a ../../subnet-dpn-peer-app/ios/Runner

```