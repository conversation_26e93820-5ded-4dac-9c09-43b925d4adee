use dpn_peernode_lib::{
    daemon::{
        direct::{DirectPeerNodeDaemon, DirectPeerNodeDaemonConfig},
        status::{ConnectionStatus, StatusUpdate},
    },
    types::AuthTokens,
};
use std::sync::{Arc, Mutex};
use tokio::runtime::Runtime;

// Use std::sync::Mutex for the runtime to avoid UnwindSafe issues
lazy_static::lazy_static! {
    static ref RUNTIME: Mutex<Runtime> = Mutex::new(Runtime::new().unwrap());
}

// Wrapper struct that holds the daemon handle in a way that's compatible with flutter_rust_bridge
pub struct PeerNodeController {
    // Store the daemon handle as an opaque pointer to avoid UnwindSafe issues
    daemon_handle: usize,
}

// Global storage for active daemons
lazy_static::lazy_static! {
    static ref DAEMON_STORAGE: Mutex<Vec<Option<Arc<DirectPeerNodeDaemon>>>> = Mutex::new(Vec::new());
}

impl PeerNodeController {
    pub fn new(admin_addr: String, access_token: String, refresh_token: String) -> Self {
        let daemon = Arc::new(DirectPeerNodeDaemon::new(DirectPeerNodeDaemonConfig {
            admin_addr,
            auth_tokens: AuthTokens {
                access_token,
                refresh_token,
            },
        }));

        // Store the daemon and get its handle
        let mut storage = DAEMON_STORAGE.lock().unwrap();
        let handle = storage.len();
        storage.push(Some(daemon));

        Self {
            daemon_handle: handle,
        }
    }

    fn get_daemon(&self) -> Arc<DirectPeerNodeDaemon> {
        let storage = DAEMON_STORAGE.lock().unwrap();
        storage[self.daemon_handle].as_ref().unwrap().clone()
    }
}

impl Drop for PeerNodeController {
    fn drop(&mut self) {
        // Clean up the daemon from storage
        let mut storage = DAEMON_STORAGE.lock().unwrap();
        if self.daemon_handle < storage.len() {
            storage[self.daemon_handle] = None;
        }
    }
}

// Initialize peer node and return controller
pub fn initialize_peer_node(
    admin_addr: String,
    access_token: String,
    refresh_token: String,
) -> PeerNodeController {
    PeerNodeController::new(admin_addr, access_token, refresh_token)
}

// Start connection
pub fn start_connection(controller: &PeerNodeController) -> Result<(), String> {
    let daemon = controller.get_daemon();
    
    let rt = RUNTIME.lock().unwrap();
    rt.block_on(async {
        daemon.start_connection().await.map_err(|e| e.to_string())
    })
}

// Stop connection
pub fn stop_connection(controller: &PeerNodeController) -> Result<(), String> {
    let daemon = controller.get_daemon();
    
    let rt = RUNTIME.lock().unwrap();
    rt.block_on(async {
        daemon.stop_connection().await.map_err(|e| e.to_string())
    })
}

// Get current connection status
pub fn get_connection_status(controller: &PeerNodeController) -> ConnectionStatus {
    let daemon = controller.get_daemon();
    
    let rt = RUNTIME.lock().unwrap();
    rt.block_on(async {
        daemon.get_current_status().await
    })
}

// Poll for status updates (non-blocking)
pub fn poll_status_update(controller: &PeerNodeController) -> Option<StatusUpdate> {
    let daemon = controller.get_daemon();
    
    let rt = RUNTIME.lock().unwrap();
    rt.block_on(async {
        daemon.poll_status_update().await
    })
}

// Update authentication tokens
pub fn update_auth_tokens(
    controller: &PeerNodeController,
    access_token: String,
    refresh_token: String,
) -> Result<(), String> {
    let daemon = controller.get_daemon();
    
    let rt = RUNTIME.lock().unwrap();
    rt.block_on(async {
        daemon.update_auth_tokens(AuthTokens {
            access_token,
            refresh_token,
        }).await.map_err(|e| e.to_string())
    })
}

// Legacy function - kept for backward compatibility but deprecated
#[deprecated(note = "Use initialize_peer_node and controller methods instead")]
pub fn run_peer_node(
    webserver_addr: String,
    webserver_port: u16,
    admin_addr: String,
    access_token: String,
    refresh_token: String,
) {
    // This function is deprecated and will be removed in future versions
    println!("Warning: run_peer_node is deprecated. Use initialize_peer_node instead.");
}