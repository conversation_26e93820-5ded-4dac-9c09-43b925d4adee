#![allow(
    non_camel_case_types,
    unused,
    clippy::redundant_closure,
    clippy::useless_conversion,
    clippy::unit_arg,
    clippy::double_parens,
    non_snake_case,
    clippy::too_many_arguments
)]
// AUTO GENERATED FILE, DO NOT EDIT.
// Generated by `flutter_rust_bridge`@ 1.82.6.

use crate::api::*;
use core::panic::UnwindSafe;
use flutter_rust_bridge::rust2dart::IntoIntoDart;
use flutter_rust_bridge::*;
use std::ffi::c_void;
use std::sync::Arc;

// Section: imports

// Section: wire functions

fn wire_run_peer_node_impl(
    port_: MessagePort,
    webserver_addr: impl Wire2Api<String> + UnwindSafe,
    webserver_port: impl Wire2Api<u16> + UnwindSafe,
    admin_addr: impl Wire2Api<String> + UnwindSafe,
    access_token: impl Wire2Api<String> + UnwindSafe,
    refresh_token: impl Wire2Api<String> + UnwindSafe,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap::<_, _, _, (), _>(
        WrapInfo {
            debug_name: "run_peer_node",
            port: Some(port_),
            mode: FfiCallMode::Normal,
        },
        move || {
            let api_webserver_addr = webserver_addr.wire2api();
            let api_webserver_port = webserver_port.wire2api();
            let api_admin_addr = admin_addr.wire2api();
            let api_access_token = access_token.wire2api();
            let api_refresh_token = refresh_token.wire2api();
            move |task_callback| {
                Result::<_, ()>::Ok(run_peer_node(
                    api_webserver_addr,
                    api_webserver_port,
                    api_admin_addr,
                    api_access_token,
                    api_refresh_token,
                ))
            }
        },
    )
}
// Section: wrapper structs

// Section: static checks

// Section: allocate functions

// Section: related functions

// Section: impl Wire2Api

pub trait Wire2Api<T> {
    fn wire2api(self) -> T;
}

impl<T, S> Wire2Api<Option<T>> for *mut S
where
    *mut S: Wire2Api<T>,
{
    fn wire2api(self) -> Option<T> {
        (!self.is_null()).then(|| self.wire2api())
    }
}

impl Wire2Api<u16> for u16 {
    fn wire2api(self) -> u16 {
        self
    }
}
impl Wire2Api<u8> for u8 {
    fn wire2api(self) -> u8 {
        self
    }
}

// Section: impl IntoDart

// Section: executor

support::lazy_static! {
    pub static ref FLUTTER_RUST_BRIDGE_HANDLER: support::DefaultHandler = Default::default();
}

#[cfg(not(target_family = "wasm"))]
#[path = "bridge_generated.io.rs"]
mod io;
#[cfg(not(target_family = "wasm"))]
pub use self::io::*;
